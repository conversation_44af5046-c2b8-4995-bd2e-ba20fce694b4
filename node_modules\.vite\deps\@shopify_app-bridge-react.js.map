{"version": 3, "sources": ["../../.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/Modal.js", "../../.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/NavMenu.js", "../../.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/TitleBar.js", "../../.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/SaveBar.js", "../../.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/hooks/useAppBridge.js"], "sourcesContent": ["import { forwardRef, useState, Children, useEffect } from 'react';\nimport ReactDOM from 'react-dom';\nimport { jsxs, jsx } from 'react/jsx-runtime';\n\n/**\n * This component is a wrapper around the App Bridge `ui-modal` element.\n * It is used to display an overlay that prevents interaction with the\n * rest of the app until dismissed.\n *\n * @see {@link https://shopify.dev/docs/api/app-bridge-library/react-components/modal}\n */\nconst Modal = /*#__PURE__*/forwardRef(function InternalModal({\n  open,\n  onShow,\n  onHide,\n  children,\n  ...rest\n}, forwardedRef) {\n  const [modal, setModal] = useState();\n  const {\n    titleBar,\n    saveBar,\n    modalContent\n  } = Children.toArray(children).reduce((acc, node) => {\n    const nodeName = getNodeName(node);\n    const isTitleBar = nodeName === 'ui-title-bar';\n    const isSaveBar = nodeName === 'ui-save-bar';\n    const belongToModalContent = !isTitleBar && !isSaveBar;\n    if (belongToModalContent) {\n      acc.modalContent.push(node);\n    }\n    return {\n      ...acc,\n      titleBar: isTitleBar ? node : acc.titleBar,\n      saveBar: isSaveBar ? node : acc.saveBar\n    };\n  }, {\n    modalContent: []\n  });\n  const contentPortal = modal && modal.content ? /*#__PURE__*/ReactDOM.createPortal(modalContent, modal.content) : null;\n  useEffect(() => {\n    if (!modal) return;\n    if (open) {\n      modal.show();\n    } else {\n      modal.hide();\n    }\n  }, [modal, open]);\n  useEffect(() => {\n    if (!modal || !onShow) return;\n    modal.addEventListener('show', onShow);\n    return () => {\n      modal.removeEventListener('show', onShow);\n    };\n  }, [modal, onShow]);\n  useEffect(() => {\n    if (!modal || !onHide) return;\n    modal.addEventListener('hide', onHide);\n    return () => {\n      modal.removeEventListener('hide', onHide);\n    };\n  }, [modal, onHide]);\n  useEffect(() => {\n    if (!modal) return;\n    return () => {\n      modal.hide();\n    };\n  }, [modal]);\n  return /*#__PURE__*/jsxs(\"ui-modal\", {\n    ...rest,\n    ref: modal => {\n      setModal(modal);\n      if (forwardedRef) {\n        if (typeof forwardedRef === 'function') {\n          forwardedRef(modal);\n        } else {\n          forwardedRef.current = modal;\n        }\n      }\n    },\n    children: [titleBar, saveBar, /*#__PURE__*/jsx(\"div\", {\n      children: contentPortal\n    })]\n  });\n});\nModal.displayName = 'ui-modal';\nfunction getNodeName(node) {\n  if (!node) return;\n  const rawNodeType = typeof node === 'object' && 'type' in node ? node.type : undefined;\n  const nodeType = typeof rawNodeType === 'string' ? rawNodeType : undefined;\n  const rawDisplayName = typeof rawNodeType === 'object' ? rawNodeType.displayName : undefined;\n  const displayName = typeof rawDisplayName === 'string' ? rawDisplayName : undefined;\n  return nodeType || displayName;\n}\n\nexport { Modal };\n", "/**\n * This component is a wrapper around the App Bridge `ui-nav-menu` element.\n * It is used to create a navigation menu for your app.\n *\n * @see {@link https://shopify.dev/docs/api/app-bridge-library/react-components/navmenu}\n */\nconst NavMenu = 'ui-nav-menu';\n\nexport { NavMenu };\n", "/**\n * This component is a wrapper around the App Bridge `ui-title-bar` element.\n * It is used to to populate the app title bar with button actions or the\n * modal header and footer when used within the Modal component.\n *\n * @see {@link https://shopify.dev/docs/api/app-bridge-library/react-components/titlebar}\n */\nconst TitleBar = 'ui-title-bar';\n\nexport { TitleBar };\n", "import { forwardRef, useState, useEffect } from 'react';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * This component is a wrapper around the App Bridge `ui-save-bar` element.\n * It is used to display a contextual save bar to signal dirty state in the app.\n *\n * @see {@link https://shopify.dev/docs/api/app-bridge-library/react-components/save-bar}\n */\nconst SaveBar = /*#__PURE__*/forwardRef(function InternalSaveBar({\n  open,\n  onShow,\n  onHide,\n  children,\n  ...rest\n}, forwardedRef) {\n  const [saveBar, setSaveBar] = useState();\n  useEffect(() => {\n    if (!saveBar) return;\n    if (open) {\n      saveBar.show();\n    } else {\n      saveBar.hide();\n    }\n  }, [saveBar, open]);\n  useEffect(() => {\n    if (!saveBar || !onShow) return;\n    saveBar.addEventListener('show', onShow);\n    return () => {\n      saveBar.removeEventListener('show', onShow);\n    };\n  }, [saveBar, onShow]);\n  useEffect(() => {\n    if (!saveBar || !onHide) return;\n    saveBar.addEventListener('hide', onHide);\n    return () => {\n      saveBar.removeEventListener('hide', onHide);\n    };\n  }, [saveBar, onHide]);\n  useEffect(() => {\n    if (!saveBar) return;\n    return () => {\n      saveBar.hide();\n    };\n  }, [saveBar]);\n  return /*#__PURE__*/jsx(\"ui-save-bar\", {\n    ...rest,\n    ref: saveBar => {\n      setSaveBar(saveBar);\n      if (forwardedRef) {\n        if (typeof forwardedRef === 'function') {\n          forwardedRef(saveBar);\n        } else {\n          forwardedRef.current = saveBar;\n        }\n      }\n    },\n    children: children\n  });\n});\nSaveBar.displayName = 'ui-save-bar';\n\nexport { SaveBar };\n", "/**\n * This proxy is used to throw a helpful error message when trying to access\n * the `shopify` global in a server environment.\n */\nconst serverProxy = new Proxy({}, {\n  get(_, prop) {\n    throw Error(`shopify.${String(prop)} can't be used in a server environment. You likely need to move this code into an Effect.`);\n  }\n});\n\n/**\n *\n * This hook returns the `shopify` global variable to use\n * App Bridge APIs such as `toast` and `resourcePicker`.\n *\n * @see {@link https://shopify.dev/docs/api/app-bridge-library/react-hooks/useappbridge}\n *\n * @example\n * ```jsx\n * import {useAppBridge} from '@shopify/app-bridge-react';\n * function GenerateBlogPostButton() {\n *   const shopify = useAppBridge();\n *\n *   function generateBlogPost() {\n *     // Handle generating\n *     shopify.toast.show('Blog post template generated');\n *   }\n *\n *   return <button onClick={generateBlogPost}>Generate Blog Post</button>;\n * }\n * ```\n *\n * @returns `shopify` variable or a Proxy that throws when incorrectly accessed when not in a browser context\n */\nfunction useAppBridge() {\n  if (typeof window === 'undefined') {\n    return serverProxy;\n  }\n  if (!window.shopify) {\n    throw Error('The shopify global is not defined. This likely means the App Bridge script tag was not added correctly to this page');\n  }\n  return window.shopify;\n}\n\nexport { useAppBridge };\n"], "mappings": ";;;;;;;;;;;;AAAA,mBAA0D;AAC1D,uBAAqB;AACrB,yBAA0B;AAS1B,IAAM,YAAqB,yBAAW,SAAS,cAAc;AAAA,EAC3D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG,cAAc;AACf,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,sBAAS,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,SAAS;AACnD,UAAM,WAAW,YAAY,IAAI;AACjC,UAAM,aAAa,aAAa;AAChC,UAAM,YAAY,aAAa;AAC/B,UAAM,uBAAuB,CAAC,cAAc,CAAC;AAC7C,QAAI,sBAAsB;AACxB,UAAI,aAAa,KAAK,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,UAAU,aAAa,OAAO,IAAI;AAAA,MAClC,SAAS,YAAY,OAAO,IAAI;AAAA,IAClC;AAAA,EACF,GAAG;AAAA,IACD,cAAc,CAAC;AAAA,EACjB,CAAC;AACD,QAAM,gBAAgB,SAAS,MAAM,UAAuB,iBAAAA,QAAS,aAAa,cAAc,MAAM,OAAO,IAAI;AACjH,8BAAU,MAAM;AACd,QAAI,CAAC,MAAO;AACZ,QAAI,MAAM;AACR,YAAM,KAAK;AAAA,IACb,OAAO;AACL,YAAM,KAAK;AAAA,IACb;AAAA,EACF,GAAG,CAAC,OAAO,IAAI,CAAC;AAChB,8BAAU,MAAM;AACd,QAAI,CAAC,SAAS,CAAC,OAAQ;AACvB,UAAM,iBAAiB,QAAQ,MAAM;AACrC,WAAO,MAAM;AACX,YAAM,oBAAoB,QAAQ,MAAM;AAAA,IAC1C;AAAA,EACF,GAAG,CAAC,OAAO,MAAM,CAAC;AAClB,8BAAU,MAAM;AACd,QAAI,CAAC,SAAS,CAAC,OAAQ;AACvB,UAAM,iBAAiB,QAAQ,MAAM;AACrC,WAAO,MAAM;AACX,YAAM,oBAAoB,QAAQ,MAAM;AAAA,IAC1C;AAAA,EACF,GAAG,CAAC,OAAO,MAAM,CAAC;AAClB,8BAAU,MAAM;AACd,QAAI,CAAC,MAAO;AACZ,WAAO,MAAM;AACX,YAAM,KAAK;AAAA,IACb;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,aAAoB,yBAAK,YAAY;AAAA,IACnC,GAAG;AAAA,IACH,KAAK,CAAAC,WAAS;AACZ,eAASA,MAAK;AACd,UAAI,cAAc;AAChB,YAAI,OAAO,iBAAiB,YAAY;AACtC,uBAAaA,MAAK;AAAA,QACpB,OAAO;AACL,uBAAa,UAAUA;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU,CAAC,UAAU,aAAsB,wBAAI,OAAO;AAAA,MACpD,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,MAAM,cAAc;AACpB,SAAS,YAAY,MAAM;AACzB,MAAI,CAAC,KAAM;AACX,QAAM,cAAc,OAAO,SAAS,YAAY,UAAU,OAAO,KAAK,OAAO;AAC7E,QAAM,WAAW,OAAO,gBAAgB,WAAW,cAAc;AACjE,QAAM,iBAAiB,OAAO,gBAAgB,WAAW,YAAY,cAAc;AACnF,QAAM,cAAc,OAAO,mBAAmB,WAAW,iBAAiB;AAC1E,SAAO,YAAY;AACrB;;;ACvFA,IAAM,UAAU;;;ACChB,IAAM,WAAW;;;ACPjB,IAAAC,gBAAgD;AAChD,IAAAC,sBAAoB;AAQpB,IAAM,cAAuB,0BAAW,SAAS,gBAAgB;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG,cAAc;AACf,QAAM,CAAC,SAAS,UAAU,QAAI,wBAAS;AACvC,+BAAU,MAAM;AACd,QAAI,CAAC,QAAS;AACd,QAAI,MAAM;AACR,cAAQ,KAAK;AAAA,IACf,OAAO;AACL,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,+BAAU,MAAM;AACd,QAAI,CAAC,WAAW,CAAC,OAAQ;AACzB,YAAQ,iBAAiB,QAAQ,MAAM;AACvC,WAAO,MAAM;AACX,cAAQ,oBAAoB,QAAQ,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,CAAC;AACpB,+BAAU,MAAM;AACd,QAAI,CAAC,WAAW,CAAC,OAAQ;AACzB,YAAQ,iBAAiB,QAAQ,MAAM;AACvC,WAAO,MAAM;AACX,cAAQ,oBAAoB,QAAQ,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,CAAC;AACpB,+BAAU,MAAM;AACd,QAAI,CAAC,QAAS;AACd,WAAO,MAAM;AACX,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AACZ,aAAoB,yBAAI,eAAe;AAAA,IACrC,GAAG;AAAA,IACH,KAAK,CAAAC,aAAW;AACd,iBAAWA,QAAO;AAClB,UAAI,cAAc;AAChB,YAAI,OAAO,iBAAiB,YAAY;AACtC,uBAAaA,QAAO;AAAA,QACtB,OAAO;AACL,uBAAa,UAAUA;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,QAAQ,cAAc;;;ACxDtB,IAAM,cAAc,IAAI,MAAM,CAAC,GAAG;AAAA,EAChC,IAAI,GAAG,MAAM;AACX,UAAM,MAAM,WAAW,OAAO,IAAI,CAAC,2FAA2F;AAAA,EAChI;AACF,CAAC;AA0BD,SAAS,eAAe;AACtB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO,SAAS;AACnB,UAAM,MAAM,qHAAqH;AAAA,EACnI;AACA,SAAO,OAAO;AAChB;", "names": ["ReactDOM", "modal", "import_react", "import_jsx_runtime", "saveBar"]}