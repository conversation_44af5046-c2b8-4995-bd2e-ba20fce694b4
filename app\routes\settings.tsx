import { PrismaClient } from "@prisma/client";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData } from "@remix-run/react";
import {
    Banner,
    BlockStack,
    Button,
    Card,
    FormLayout,
    InlineStack,
    Layout,
    Page,
    TextField,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";

const prisma = new PrismaClient();

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  // Try to get existing API key from database
  const shopSettings = await prisma.shopSettings.findUnique({
    where: { shop: session.shop },
  });

  return json({
    shop: session.shop,
    apiKey: shopSettings?.apiKey || "",
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const apiKey = formData.get("apiKey") as string;

  if (!apiKey || apiKey.trim() === "") {
    return json(
      { error: "API Key is required", success: false },
      { status: 400 }
    );
  }

  try {
    // Save the API key to the database
    await prisma.shopSettings.upsert({
      where: { shop: session.shop },
      update: { apiKey },
      create: { shop: session.shop, apiKey },
    });

    return json({ success: true, message: "API Key saved successfully!" });
  } catch (error) {
    console.error("Error saving API key:", error);
    return json(
      { error: "Failed to save API Key", success: false },
      { status: 500 }
    );
  }
};

export default function Settings() {
  const { shop, apiKey } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();

  return (
    <Page
      title="Settings"
      backAction={{ content: "Home", url: "/" }}
    >
      <Layout>
        <Layout.Section>
          {actionData?.success && (
            <Banner tone="success" title="Success">
              {actionData.message}
            </Banner>
          )}

          {actionData?.error && (
            <Banner tone="critical" title="Error">
              {actionData.error}
            </Banner>
          )}

          <Card>
            <BlockStack gap="400">
              <BlockStack gap="200">
                <h2 style={{ fontSize: "1.25rem", fontWeight: "600" }}>
                  Enable Backend Integration
                </h2>
                <p style={{ color: "#6b7280" }}>
                  Configure your API key to connect with the Enable backend service.
                  This will allow your app to communicate with the loyalty program system.
                </p>
              </BlockStack>

              <Form method="post">
                <FormLayout>
                  <TextField
                    label="API Key"
                    name="apiKey"
                    defaultValue={apiKey}
                    placeholder="Enter your Enable backend API key"
                    helpText="You can find your API key in the Enable backend dashboard"
                    autoComplete="off"
                    type="password"
                  />

                  <InlineStack gap="300">
                    <Button submit variant="primary">
                      Save API Key
                    </Button>
                    <Link to="/">
                      <Button>
                        Cancel
                      </Button>
                    </Link>
                  </InlineStack>
                </FormLayout>
              </Form>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
