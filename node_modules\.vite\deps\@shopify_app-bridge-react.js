import {
  require_jsx_runtime
} from "./chunk-5WZE2OOG.js";
import {
  require_react_dom
} from "./chunk-WHGUC2DB.js";
import {
  __toESM,
  require_react
} from "./chunk-H6BSJVLB.js";

// node_modules/.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/Modal.js
var import_react = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Modal = (0, import_react.forwardRef)(function InternalModal({
  open,
  onShow,
  onHide,
  children,
  ...rest
}, forwardedRef) {
  const [modal, setModal] = (0, import_react.useState)();
  const {
    titleBar,
    saveBar,
    modalContent
  } = import_react.Children.toArray(children).reduce((acc, node) => {
    const nodeName = getNodeName(node);
    const isTitleBar = nodeName === "ui-title-bar";
    const isSaveBar = nodeName === "ui-save-bar";
    const belongToModalContent = !isTitleBar && !isSaveBar;
    if (belongToModalContent) {
      acc.modalContent.push(node);
    }
    return {
      ...acc,
      titleBar: isTitleBar ? node : acc.titleBar,
      saveBar: isSaveBar ? node : acc.saveBar
    };
  }, {
    modalContent: []
  });
  const contentPortal = modal && modal.content ? import_react_dom.default.createPortal(modalContent, modal.content) : null;
  (0, import_react.useEffect)(() => {
    if (!modal) return;
    if (open) {
      modal.show();
    } else {
      modal.hide();
    }
  }, [modal, open]);
  (0, import_react.useEffect)(() => {
    if (!modal || !onShow) return;
    modal.addEventListener("show", onShow);
    return () => {
      modal.removeEventListener("show", onShow);
    };
  }, [modal, onShow]);
  (0, import_react.useEffect)(() => {
    if (!modal || !onHide) return;
    modal.addEventListener("hide", onHide);
    return () => {
      modal.removeEventListener("hide", onHide);
    };
  }, [modal, onHide]);
  (0, import_react.useEffect)(() => {
    if (!modal) return;
    return () => {
      modal.hide();
    };
  }, [modal]);
  return (0, import_jsx_runtime.jsxs)("ui-modal", {
    ...rest,
    ref: (modal2) => {
      setModal(modal2);
      if (forwardedRef) {
        if (typeof forwardedRef === "function") {
          forwardedRef(modal2);
        } else {
          forwardedRef.current = modal2;
        }
      }
    },
    children: [titleBar, saveBar, (0, import_jsx_runtime.jsx)("div", {
      children: contentPortal
    })]
  });
});
Modal.displayName = "ui-modal";
function getNodeName(node) {
  if (!node) return;
  const rawNodeType = typeof node === "object" && "type" in node ? node.type : void 0;
  const nodeType = typeof rawNodeType === "string" ? rawNodeType : void 0;
  const rawDisplayName = typeof rawNodeType === "object" ? rawNodeType.displayName : void 0;
  const displayName = typeof rawDisplayName === "string" ? rawDisplayName : void 0;
  return nodeType || displayName;
}

// node_modules/.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/NavMenu.js
var NavMenu = "ui-nav-menu";

// node_modules/.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/TitleBar.js
var TitleBar = "ui-title-bar";

// node_modules/.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/components/SaveBar.js
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var SaveBar = (0, import_react2.forwardRef)(function InternalSaveBar({
  open,
  onShow,
  onHide,
  children,
  ...rest
}, forwardedRef) {
  const [saveBar, setSaveBar] = (0, import_react2.useState)();
  (0, import_react2.useEffect)(() => {
    if (!saveBar) return;
    if (open) {
      saveBar.show();
    } else {
      saveBar.hide();
    }
  }, [saveBar, open]);
  (0, import_react2.useEffect)(() => {
    if (!saveBar || !onShow) return;
    saveBar.addEventListener("show", onShow);
    return () => {
      saveBar.removeEventListener("show", onShow);
    };
  }, [saveBar, onShow]);
  (0, import_react2.useEffect)(() => {
    if (!saveBar || !onHide) return;
    saveBar.addEventListener("hide", onHide);
    return () => {
      saveBar.removeEventListener("hide", onHide);
    };
  }, [saveBar, onHide]);
  (0, import_react2.useEffect)(() => {
    if (!saveBar) return;
    return () => {
      saveBar.hide();
    };
  }, [saveBar]);
  return (0, import_jsx_runtime2.jsx)("ui-save-bar", {
    ...rest,
    ref: (saveBar2) => {
      setSaveBar(saveBar2);
      if (forwardedRef) {
        if (typeof forwardedRef === "function") {
          forwardedRef(saveBar2);
        } else {
          forwardedRef.current = saveBar2;
        }
      }
    },
    children
  });
});
SaveBar.displayName = "ui-save-bar";

// node_modules/.pnpm/@shopify+app-bridge-react@4_5912973be07495292680fdd930ace455/node_modules/@shopify/app-bridge-react/build/esm/hooks/useAppBridge.js
var serverProxy = new Proxy({}, {
  get(_, prop) {
    throw Error(`shopify.${String(prop)} can't be used in a server environment. You likely need to move this code into an Effect.`);
  }
});
function useAppBridge() {
  if (typeof window === "undefined") {
    return serverProxy;
  }
  if (!window.shopify) {
    throw Error("The shopify global is not defined. This likely means the App Bridge script tag was not added correctly to this page");
  }
  return window.shopify;
}
export {
  Modal,
  NavMenu,
  SaveBar,
  TitleBar,
  useAppBridge
};
//# sourceMappingURL=@shopify_app-bridge-react.js.map
