import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Button,
  Text,
  BlockStack,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  return json({
    shop: session.shop,
  });
};

export default function Index() {
  const { shop } = useLoaderData<typeof loader>();

  return (
    <Page title="Enable Loyalty App">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingMd" as="h2">
                Welcome to Enable Loyalty
              </Text>
              <Text as="p">
                This app helps you integrate with the Enable backend for loyalty programs.
                Get started by configuring your API settings.
              </Text>
              <Text as="p" tone="subdued">
                Connected to: {shop}
              </Text>
              <Link to="/settings">
                <Button variant="primary">
                  Go to Settings
                </Button>
              </Link>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
